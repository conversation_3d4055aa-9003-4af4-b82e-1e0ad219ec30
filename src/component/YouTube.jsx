import React from 'react';
import { Play, Users, Video, Calendar, ExternalLink } from 'lucide-react';

const YouTube = ({ darkMode }) => {
  // YouTube channel data - update with your actual channel information
  const channelInfo = {
    name: "<PERSON>urious<PERSON>oder",
    description: "Welcome to my coding journey! I share tutorials, project walkthroughs, and programming tips to help fellow developers learn and grow. From React tutorials to game development with Unity, join me as I explore the world of programming!",
    subscribers: "Coming Soon",
    videos: "Coming Soon",
    channelUrl: "https://youtube.com/@curiouscoder", // Update with your actual channel URL
    uploadSchedule: "New videos every week"
  };

  // Featured videos - update with your actual video data
  const featuredVideos = [
    {
      id: 1,
      title: "Building a React Portfolio Website",
      description: "Complete tutorial on creating a modern portfolio website using React and Tailwind CSS with responsive design and dark mode.",
      thumbnail: "https://placehold.co/480x360/4F46E5/FFFFFF/png?text=React+Portfolio+Tutorial",
      duration: "15:30",
      views: "Coming Soon",
      uploadDate: "Coming Soon",
      videoUrl: "#" // Update with actual video URL when available
    },
    {
      id: 2,
      title: "Unity Game Development Basics",
      description: "Learn the fundamentals of game development with Unity and C# - perfect for beginners starting their game dev journey.",
      thumbnail: "https://placehold.co/480x360/059669/FFFFFF/png?text=Unity+Game+Dev",
      duration: "22:45",
      views: "Coming Soon",
      uploadDate: "Coming Soon",
      videoUrl: "#" // Update with actual video URL when available
    },
    {
      id: 3,
      title: "JavaScript Tips and Tricks",
      description: "Essential JavaScript concepts, ES6+ features, and modern development practices every developer should master.",
      thumbnail: "https://placehold.co/480x360/F59E0B/FFFFFF/png?text=JavaScript+Tips",
      duration: "18:20",
      views: "Coming Soon",
      uploadDate: "Coming Soon",
      videoUrl: "#" // Update with actual video URL when available
    }
  ];

  const VideoCard = ({ video }) => (
    <div className={`rounded-xl overflow-hidden shadow-lg transform hover:-translate-y-2 transition-all duration-300 ${
      darkMode ? 'bg-gray-800 border border-gray-700' : 'bg-white border border-gray-100'
    }`}>
      <div className="relative group cursor-pointer">
        <img 
          src={video.thumbnail} 
          alt={video.title}
          className="w-full h-48 object-cover"
          onError={(e) => {
            e.target.src = 'https://placehold.co/400x225/png?text=Video+Thumbnail';
          }}
        />
        <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <Play className="w-16 h-16 text-white" />
        </div>
        <div className="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
          {video.duration}
        </div>
      </div>
      
      <div className="p-6">
        <h3 className={`text-lg font-semibold mb-2 line-clamp-2 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
          {video.title}
        </h3>
        <p className={`text-sm mb-4 line-clamp-2 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
          {video.description}
        </p>
        
        <div className={`flex justify-between items-center text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
          <span>{video.views} views</span>
          <span>{video.uploadDate}</span>
        </div>
        
        <a
          href={video.videoUrl}
          target="_blank"
          rel="noopener noreferrer"
          className="mt-4 inline-flex items-center text-red-500 hover:text-red-600 transition-colors text-sm font-medium"
        >
          Watch Video <ExternalLink className="ml-1 w-4 h-4" />
        </a>
      </div>
    </div>
  );

  return (
    <section className={`py-20 px-4 ${darkMode ? 'bg-gray-800' : 'bg-white'}`} id="youtube">
      <div className="max-w-6xl mx-auto">
        {/* Channel Header */}
        <div className="text-center mb-16">
          <div className="flex justify-center items-center mb-6">
            <div className="bg-red-500 p-4 rounded-full mr-4">
              <Play className="w-8 h-8 text-white" />
            </div>
            <div>
              <h2 className={`text-4xl font-bold font-playfair ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                {channelInfo.name}
              </h2>
              <p className={`text-lg ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                YouTube Channel
              </p>
            </div>
          </div>
          
          <p className={`text-lg max-w-3xl mx-auto mb-8 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
            {channelInfo.description}
          </p>

          {/* Channel Stats */}
          <div className="grid md:grid-cols-3 gap-6 max-w-2xl mx-auto mb-8">
            <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
              <Users className={`w-8 h-8 mx-auto mb-2 ${darkMode ? 'text-red-400' : 'text-red-500'}`} />
              <div className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                {channelInfo.subscribers}
              </div>
              <div className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                Subscribers
              </div>
            </div>
            
            <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
              <Video className={`w-8 h-8 mx-auto mb-2 ${darkMode ? 'text-red-400' : 'text-red-500'}`} />
              <div className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                {channelInfo.videos}
              </div>
              <div className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                Videos
              </div>
            </div>
            
            <div className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
              <Calendar className={`w-8 h-8 mx-auto mb-2 ${darkMode ? 'text-red-400' : 'text-red-500'}`} />
              <div className={`text-lg font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                Weekly
              </div>
              <div className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                Upload Schedule
              </div>
            </div>
          </div>

          <a
            href={channelInfo.channelUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center bg-red-500 hover:bg-red-600 text-white px-8 py-3 rounded-full font-semibold transition-colors duration-300"
          >
            Visit Channel <ExternalLink className="ml-2 w-5 h-5" />
          </a>
        </div>

        {/* Featured Videos */}
        <div>
          <h3 className={`text-2xl font-bold mb-8 text-center ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            Featured Videos
          </h3>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {featuredVideos.map((video) => (
              <VideoCard key={video.id} video={video} />
            ))}
          </div>
        </div>

        {/* Call to Action */}
        <div className={`mt-16 p-8 rounded-xl text-center ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
          <h3 className={`text-2xl font-bold mb-4 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            Stay Updated!
          </h3>
          <p className={`text-lg mb-6 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
            Subscribe to my channel for the latest coding tutorials, project walkthroughs, and programming tips.
          </p>
          <a
            href={channelInfo.channelUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center bg-red-500 hover:bg-red-600 text-white px-6 py-3 rounded-lg font-semibold transition-colors duration-300"
          >
            Subscribe Now <Play className="ml-2 w-5 h-5" />
          </a>
        </div>
      </div>
    </section>
  );
};

export default YouTube;
