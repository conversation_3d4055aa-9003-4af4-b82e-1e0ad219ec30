import React, { useState } from 'react';
import { Download, Eye, X, FileText } from 'lucide-react';

const Resume = ({ darkMode }) => {
  const [showModal, setShowModal] = useState(false);

  const resumeData = {
    pdfUrl: "/resume.pdf",
    previewImage: "/resume-preview.jpg",
    downloadUrl: "/resume.pdf",
    lastUpdated: "January 2025"
  };

  const openModal = () => setShowModal(true);
  const closeModal = () => setShowModal(false);

  const handleDownload = () => {
    const link = document.createElement('a');
    link.href = resumeData.downloadUrl;
    link.download = '<PERSON><PERSON><PERSON>_Jain_Resume.pdf';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <>
      <section className={`py-20 px-4 ${darkMode ? 'bg-gray-900' : 'bg-gray-50'}`} id="resume">
        <div className="max-w-4xl mx-auto text-center">
          <div className="mb-8">
            <div className={`inline-flex items-center justify-center w-20 h-20 rounded-full mb-6 ${
              darkMode ? 'bg-blue-600' : 'bg-blue-100'
            }`}>
              <FileText className={`w-10 h-10 ${darkMode ? 'text-white' : 'text-blue-600'}`} />
            </div>
            
            <h2 className={`text-4xl font-bold mb-4 font-playfair ${darkMode ? 'text-white' : 'text-gray-900'}`}>
              My Resume
            </h2>
            
            <p className={`text-lg max-w-2xl mx-auto mb-8 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
              Download my resume to learn more about my experience, skills, and educational background.
            </p>
          </div>

          <div className={`max-w-md mx-auto rounded-xl shadow-lg overflow-hidden mb-8 ${
            darkMode ? 'bg-gray-800 border border-gray-700' : 'bg-white border border-gray-100'
          }`}>
            <div className="relative group cursor-pointer" onClick={openModal}>
              <img 
                src={resumeData.previewImage}
                alt="Resume Preview"
                className="w-full h-64 object-cover"
                onError={(e) => {
                  e.target.src = 'https://placehold.co/400x500/png?text=Resume+Preview';
                }}
              />
              <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <Eye className="w-12 h-12 text-white" />
              </div>
            </div>
            
            <div className="p-6">
              <h3 className={`text-xl font-semibold mb-2 ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                Nitya Jain - Resume
              </h3>
              <p className={`text-sm mb-4 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                Full Stack Developer & Game Developer
              </p>
              <p className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                Last updated: {resumeData.lastUpdated}
              </p>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button
              onClick={openModal}
              className={`inline-flex items-center px-6 py-3 rounded-lg font-semibold transition-colors duration-300 ${
                darkMode 
                  ? 'bg-blue-600 hover:bg-blue-700 text-white' 
                  : 'bg-blue-500 hover:bg-blue-600 text-white'
              }`}
            >
              <Eye className="mr-2 w-5 h-5" />
              View Resume
            </button>
            
            <button
              onClick={handleDownload}
              className={`inline-flex items-center px-6 py-3 rounded-lg font-semibold transition-colors duration-300 ${
                darkMode 
                  ? 'bg-green-600 hover:bg-green-700 text-white' 
                  : 'bg-green-500 hover:bg-green-600 text-white'
              }`}
            >
              <Download className="mr-2 w-5 h-5" />
              Download PDF
            </button>
          </div>
        </div>
      </section>

      {showModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-75">
          <div className={`relative w-full max-w-4xl max-h-[90vh] rounded-lg overflow-hidden ${
            darkMode ? 'bg-gray-800' : 'bg-white'
          }`}>
            <div className={`flex justify-between items-center p-4 border-b ${
              darkMode ? 'border-gray-700' : 'border-gray-200'
            }`}>
              <h3 className={`text-lg font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                Resume Preview
              </h3>
              <div className="flex gap-2">
                <button
                  onClick={handleDownload}
                  className={`p-2 rounded-lg transition-colors ${
                    darkMode ? 'hover:bg-gray-700 text-gray-300' : 'hover:bg-gray-100 text-gray-600'
                  }`}
                  title="Download Resume"
                >
                  <Download className="w-5 h-5" />
                </button>
                <button
                  onClick={closeModal}
                  className={`p-2 rounded-lg transition-colors ${
                    darkMode ? 'hover:bg-gray-700 text-gray-300' : 'hover:bg-gray-100 text-gray-600'
                  }`}
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
            </div>
            
            <div className="p-4 h-[calc(90vh-80px)] overflow-auto">
              <iframe
                src={resumeData.pdfUrl}
                className="w-full h-full border-0"
                title="Resume PDF"
              />
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default Resume;
