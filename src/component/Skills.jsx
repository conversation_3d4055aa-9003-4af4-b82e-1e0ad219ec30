import React from 'react';
import { Code, Database, Wrench, Gamepad2, Globe, Smartphone } from 'lucide-react';

const Skills = ({ darkMode }) => {
  const skillCategories = [
    {
      title: "Frontend Development",
      icon: <Globe className="w-6 h-6" />,
      skills: [
        { name: "React", level: 90, color: "bg-blue-500" },
        { name: "JavaScript", level: 85, color: "bg-yellow-500" },
        { name: "HTML/CSS", level: 95, color: "bg-orange-500" },
        { name: "Tailwind CSS", level: 88, color: "bg-cyan-500" },
        { name: "<PERSON>tra<PERSON>", level: 80, color: "bg-purple-500" }
      ]
    },
    {
      title: "Backend & Frameworks",
      icon: <Database className="w-6 h-6" />,
      skills: [
        { name: "Node.js", level: 75, color: "bg-green-500" },
        { name: "Next.js", level: 82, color: "bg-gray-700" },
        { name: "Express.js", level: 70, color: "bg-gray-600" }
      ]
    },
    {
      title: "Programming Languages",
      icon: <Code className="w-6 h-6" />,
      skills: [
        { name: "JavaScript", level: 85, color: "bg-yellow-500" },
        { name: "TypeScript", level: 75, color: "bg-blue-600" },
        { name: "C#", level: 70, color: "bg-purple-600" }
      ]
    },
    {
      title: "Tools & Technologies",
      icon: <Wrench className="w-6 h-6" />,
      skills: [
        { name: "Git", level: 80, color: "bg-red-500" },
        { name: "Vite", level: 85, color: "bg-purple-500" },
        { name: "Netlify", level: 75, color: "bg-teal-500" },
        { name: "Unity", level: 70, color: "bg-gray-800" }
      ]
    },
    {
      title: "Game Development",
      icon: <Gamepad2 className="w-6 h-6" />,
      skills: [
        { name: "Unity", level: 70, color: "bg-gray-800" },
        { name: "C# for Games", level: 68, color: "bg-purple-600" },
        { name: "Game Design", level: 75, color: "bg-indigo-500" }
      ]
    },
    {
      title: "Mobile & Responsive",
      icon: <Smartphone className="w-6 h-6" />,
      skills: [
        { name: "Responsive Design", level: 90, color: "bg-pink-500" },
        { name: "Mobile-First", level: 85, color: "bg-rose-500" },
        { name: "Cross-Browser", level: 80, color: "bg-emerald-500" }
      ]
    }
  ];

  const SkillBar = ({ skill }) => (
    <div className="mb-4">
      <div className="flex justify-between items-center mb-2">
        <span className={`text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
          {skill.name}
        </span>
        <span className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
          {skill.level}%
        </span>
      </div>
      <div className={`w-full rounded-full h-2 ${darkMode ? 'bg-gray-700' : 'bg-gray-200'}`}>
        <div
          className={`h-2 rounded-full ${skill.color} transition-all duration-1000 ease-out`}
          style={{ width: `${skill.level}%` }}
        ></div>
      </div>
    </div>
  );

  return (
    <section className={`min-h-screen flex items-center relative overflow-hidden py-20 px-4 ${darkMode ? 'bg-gray-900' : 'bg-gray-50'}`} id="skills">
      {/* Gradient Background */}
      <div className={`absolute inset-0 ${darkMode ? 'bg-gradient-to-br from-gray-800 via-gray-900 to-gray-800' : 'bg-gradient-to-br from-blue-50/80 via-purple-50/80 to-pink-50/80'}`}></div>

      {/* 3D Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Floating Cubes */}
        <div className={`absolute top-20 left-20 w-12 h-12 animate-float-slow ${darkMode ? 'opacity-20' : 'opacity-40'}`}>
          <div className="cube">
            <div className={`cube-face front ${darkMode ? 'bg-purple-400' : 'bg-purple-500'}`}></div>
            <div className={`cube-face back ${darkMode ? 'bg-purple-600' : 'bg-purple-700'}`}></div>
            <div className={`cube-face right ${darkMode ? 'bg-purple-500' : 'bg-purple-600'}`}></div>
            <div className={`cube-face left ${darkMode ? 'bg-purple-500' : 'bg-purple-600'}`}></div>
            <div className={`cube-face top ${darkMode ? 'bg-purple-400' : 'bg-purple-500'}`}></div>
            <div className={`cube-face bottom ${darkMode ? 'bg-purple-600' : 'bg-purple-700'}`}></div>
          </div>
        </div>

        {/* Floating circles */}
        <div className={`absolute bottom-32 right-1/4 w-16 h-16 rounded-full animate-float-slow animation-delay-1000 ${darkMode ? 'bg-blue-500/20' : 'bg-blue-500/40'} backdrop-blur-sm`}></div>
        <div className={`absolute top-1/3 right-20 w-10 h-10 rounded-full animate-float-slow animation-delay-3000 ${darkMode ? 'bg-green-500/20' : 'bg-green-500/40'} backdrop-blur-sm`}></div>
      </div>

      {/* Animated Gradient Orbs */}
      <div className={`absolute top-0 -left-4 w-72 h-72 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob ${darkMode ? 'hidden' : ''}`}></div>
      <div className={`absolute top-0 -right-4 w-72 h-72 bg-blue-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000 ${darkMode ? 'hidden' : ''}`}></div>
      <div className={`absolute -bottom-8 left-20 w-72 h-72 bg-pink-300 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000 ${darkMode ? 'hidden' : ''}`}></div>

      <div className="max-w-6xl mx-auto relative z-10">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-6xl font-bold mb-6 font-playfair">
            Skills & Expertise
          </h2>
          <p className={`text-xl md:text-2xl max-w-3xl mx-auto ${darkMode ? 'text-gray-300' : 'text-gray-600'} font-poppins`}>
            A comprehensive showcase of my technical skills and proficiency levels across various technologies and frameworks.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {skillCategories.map((category, index) => (
            <div
              key={index}
              className={`p-8 rounded-2xl shadow-2xl transform hover:-translate-y-4 hover:scale-105 transition-all duration-500 backdrop-blur-sm border ${
                darkMode
                  ? 'bg-gray-800/80 border-gray-700/50 hover:bg-gray-700/90'
                  : 'bg-white/90 border-white/50 hover:bg-white/95'
              }`}
              style={{
                background: darkMode
                  ? 'linear-gradient(135deg, rgba(31, 41, 55, 0.8) 0%, rgba(17, 24, 39, 0.9) 100%)'
                  : 'linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.95) 100%)'
              }}
            >
              <div className="flex items-center mb-8">
                <div className={`p-4 rounded-xl mr-4 shadow-lg transform hover:rotate-12 transition-transform duration-300 ${
                  darkMode ? 'bg-gradient-to-br from-blue-500 to-purple-600' : 'bg-gradient-to-br from-blue-400 to-purple-500'
                }`}>
                  <div className="text-white">
                    {category.icon}
                  </div>
                </div>
                <h3 className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'} font-playfair`}>
                  {category.title}
                </h3>
              </div>
              
              <div className="space-y-4">
                {category.skills.map((skill, skillIndex) => (
                  <SkillBar key={skillIndex} skill={skill} />
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* Additional Skills Summary */}
        <div className={`mt-20 p-10 rounded-3xl shadow-2xl backdrop-blur-sm border transform hover:scale-105 transition-all duration-500 ${
          darkMode
            ? 'bg-gray-800/80 border-gray-700/50'
            : 'bg-white/90 border-white/50'
        }`}
        style={{
          background: darkMode
            ? 'linear-gradient(135deg, rgba(31, 41, 55, 0.8) 0%, rgba(17, 24, 39, 0.9) 100%)'
            : 'linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.95) 100%)'
        }}>
          <h3 className={`text-3xl font-bold mb-10 text-center ${darkMode ? 'text-white' : 'text-gray-900'} font-playfair`}>
            Quick Overview
          </h3>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 text-center">
            <div className={`p-6 rounded-2xl transform hover:-translate-y-2 transition-all duration-300 ${
              darkMode ? 'bg-gradient-to-br from-blue-600/20 to-blue-800/20' : 'bg-gradient-to-br from-blue-50 to-blue-100'
            }`}>
              <div className={`text-4xl font-bold mb-3 ${darkMode ? 'text-blue-400' : 'text-blue-600'} font-playfair`}>
                3+
              </div>
              <div className={`text-base font-medium ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                Years of Experience
              </div>
            </div>
            <div className={`p-6 rounded-2xl transform hover:-translate-y-2 transition-all duration-300 ${
              darkMode ? 'bg-gradient-to-br from-green-600/20 to-green-800/20' : 'bg-gradient-to-br from-green-50 to-green-100'
            }`}>
              <div className={`text-4xl font-bold mb-3 ${darkMode ? 'text-green-400' : 'text-green-600'} font-playfair`}>
                15+
              </div>
              <div className={`text-base font-medium ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                Technologies
              </div>
            </div>
            <div className={`p-6 rounded-2xl transform hover:-translate-y-2 transition-all duration-300 ${
              darkMode ? 'bg-gradient-to-br from-purple-600/20 to-purple-800/20' : 'bg-gradient-to-br from-purple-50 to-purple-100'
            }`}>
              <div className={`text-4xl font-bold mb-3 ${darkMode ? 'text-purple-400' : 'text-purple-600'} font-playfair`}>
                8+
              </div>
              <div className={`text-base font-medium ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                Projects Completed
              </div>
            </div>
            <div className={`p-6 rounded-2xl transform hover:-translate-y-2 transition-all duration-300 ${
              darkMode ? 'bg-gradient-to-br from-orange-600/20 to-orange-800/20' : 'bg-gradient-to-br from-orange-50 to-orange-100'
            }`}>
              <div className={`text-4xl font-bold mb-3 ${darkMode ? 'text-orange-400' : 'text-orange-600'} font-playfair`}>
                100%
              </div>
              <div className={`text-base font-medium ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                Passion for Coding
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Skills;
