{"name": "nitya-portfolio", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.15.13", "@mui/material": "^5.15.13", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.18", "framer-motion": "^11.0.14", "lucide-react": "^0.487.0", "nodemailer": "^6.9.12", "postcss": "^8.4.35", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^5.0.1", "react-intersection-observer": "^9.8.1", "react-router-dom": "^6.22.3", "react-scroll": "^1.9.0", "styled-components": "^6.1.8", "tailwindcss": "^3.4.1", "vite": "^5.1.7"}, "devDependencies": {"@types/react": "^18.2.64", "@types/react-dom": "^18.2.21", "eslint": "^8.57.0", "eslint-plugin-react": "^7.34.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5"}}